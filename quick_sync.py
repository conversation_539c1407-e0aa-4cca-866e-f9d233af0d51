#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速同步脚本 - 提供常见的同步场景
"""

import os
import sys
import subprocess
from typing import Optional

def run_sync(start_offset: Optional[int] = None, start_page: Optional[int] = None, 
             max_records: Optional[int] = None, project_code: Optional[str] = None):
    """运行同步脚本"""
    cmd = [sys.executable, "sync_data.py"]
    
    if start_offset is not None:
        cmd.extend(["--start-offset", str(start_offset)])
    
    if start_page is not None:
        cmd.extend(["--start-page", str(start_page)])
    
    if max_records is not None:
        cmd.extend(["--max-records", str(max_records)])
    
    if project_code is not None:
        cmd.extend(["--project-code", project_code])
    
    print(f"执行命令: {' '.join(cmd)}")
    return subprocess.run(cmd)

def main():
    """主函数 - 提供交互式菜单"""
    print("=== 数据同步快速启动脚本 ===")
    print("请选择同步场景:")
    print("1. 从头开始同步（0-20000）")
    print("2. 继续同步（20001-40000）")
    print("3. 继续同步（40001-60000）")
    print("4. 继续同步（60001-80000）")
    print("5. 自定义偏移量同步")
    print("6. 自定义页数同步")
    print("7. 测试同步（少量数据）")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请输入选择 (0-7): ").strip()
            
            if choice == "0":
                print("退出程序")
                break
            
            elif choice == "1":
                print("\n开始从头同步（0-20000）...")
                run_sync(start_offset=0, max_records=20000)
            
            elif choice == "2":
                print("\n开始继续同步（20001-40000）...")
                run_sync(start_offset=20000, max_records=20000)
            
            elif choice == "3":
                print("\n开始继续同步（40001-60000）...")
                run_sync(start_offset=40000, max_records=20000)
            
            elif choice == "4":
                print("\n开始继续同步（60001-80000）...")
                run_sync(start_offset=60000, max_records=20000)
            
            elif choice == "5":
                try:
                    offset = int(input("请输入起始偏移量: "))
                    max_records = input("请输入最大记录数（回车使用默认值）: ").strip()
                    max_records = int(max_records) if max_records else None
                    
                    print(f"\n开始自定义偏移量同步（从第{offset}条开始）...")
                    run_sync(start_offset=offset, max_records=max_records)
                except ValueError:
                    print("输入无效，请输入数字")
            
            elif choice == "6":
                try:
                    page = int(input("请输入起始页数: "))
                    max_records = input("请输入最大记录数（回车使用默认值）: ").strip()
                    max_records = int(max_records) if max_records else None
                    
                    print(f"\n开始自定义页数同步（从第{page}页开始）...")
                    run_sync(start_page=page, max_records=max_records)
                except ValueError:
                    print("输入无效，请输入数字")
            
            elif choice == "7":
                print("\n开始测试同步（同步10条记录）...")
                run_sync(start_offset=0, max_records=10)
            
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"发生错误: {str(e)}")

def show_help():
    """显示帮助信息"""
    print("=== 快速同步脚本帮助 ===")
    print("\n常用命令示例:")
    print("python quick_sync.py                    # 交互式菜单")
    print("python sync_data.py --start-offset 20000 --max-records 20000")
    print("python sync_data.py --start-page 201 --max-records 20000")
    print("\n环境变量配置:")
    print("请确保.env文件中包含以下配置:")
    print("- FEISHU_APP_ID")
    print("- FEISHU_APP_SECRET")
    print("- FEISHU_APP_TOKEN")
    print("- FEISHU_TABLE_ID")
    print("- DB_API_BASE_URL")
    print("\n更多信息请查看 使用说明.md")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ["--help", "-h", "help"]:
        show_help()
    else:
        # 检查必要文件是否存在
        if not os.path.exists("sync_data.py"):
            print("错误: 找不到 sync_data.py 文件")
            sys.exit(1)
        
        if not os.path.exists(".env"):
            print("警告: 找不到 .env 文件，请确保环境变量已正确配置")
        
        main()