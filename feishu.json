{"code": 0, "data": {"has_more": true, "items": [{"field_id": "fldfX7JJlA", "field_name": "KOL ID", "is_primary": true, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld474L7vA", "field_name": "Manual Status", "is_primary": false, "property": {"options": [{"color": 0, "id": "opt8GIBkez", "name": "OOG120-Misma<PERSON>"}, {"color": 1, "id": "optT1ecqza", "name": "OOG120-Matched"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld9z4F71u", "field_name": "Email", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld0M0k2s5", "field_name": "Account link", "is_primary": false, "property": null, "type": 15, "ui_type": "Url"}, {"field_id": "fldAZhKkcc", "field_name": "Send Status", "is_primary": false, "property": {"options": [{"color": 0, "id": "optyMoOWJ0", "name": "第四轮"}, {"color": 8, "id": "optM5KeDS9", "name": "Round No.32"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fld3o9DqAx", "field_name": "Send Date", "is_primary": false, "property": {"auto_fill": false, "date_formatter": "yyyy/MM/dd"}, "type": 5, "ui_type": "DateTime"}, {"field_id": "fldtPzB7PT", "field_name": "KOL Name", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fldgCMdgRT", "field_name": "Bio", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld6E3EtrK", "field_name": "Export Date", "is_primary": false, "property": {"auto_fill": true, "date_formatter": "yyyy/MM/dd"}, "type": 5, "ui_type": "DateTime"}, {"field_id": "fld0WDvqqK", "field_name": "Source", "is_primary": false, "property": {"options": [{"color": 0, "id": "opt45iCdi3", "name": "Collabstr"}, {"color": 1, "id": "optWy0UYXu", "name": "Manual"}, {"color": 2, "id": "opt9KMQetm", "name": "<PERSON>reable"}, {"color": 3, "id": "opt1115032617", "name": "<PERSON><PERSON><PERSON>"}, {"color": 4, "id": "opt962695875", "name": "Modas<PERSON>"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fldH8kFQIv", "field_name": "Filter", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld4SFEiCA", "field_name": "Gender", "is_primary": false, "property": {"options": [{"color": 0, "id": "optFTjgax9", "name": "Male"}, {"color": 1, "id": "opt0ggyjVn", "name": "Female"}, {"color": 2, "id": "opt553142582", "name": "MALE"}, {"color": 6, "id": "opt199jkDe", "name": "LGBT"}, {"color": 3, "id": "opt1202605003", "name": "FEMALE"}]}, "type": 3, "ui_type": "SingleSelect"}, {"field_id": "fldz8OOg2o", "field_name": "Tag", "is_primary": false, "property": {"options": [{"color": 7, "id": "optrNLU91H", "name": "fitness"}, {"color": 6, "id": "optBCrTW6s", "name": "model"}]}, "type": 4, "ui_type": "MultiSelect"}, {"field_id": "fld0FhflKc", "field_name": "Language", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fld46fWZdV", "field_name": "Location", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}, {"field_id": "fldQ5A1fxI", "field_name": "Followers(K)", "is_primary": false, "property": {"formatter": "0.0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fldyy9fTfl", "field_name": "Likes(K)", "is_primary": false, "property": {"formatter": "0.0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fldtXe7t67", "field_name": "Mean Views(K)", "is_primary": false, "property": {"formatter": "0.0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fldvOXBJ4Y", "field_name": "Median Views(K)", "is_primary": false, "property": {"formatter": "0.0"}, "type": 2, "ui_type": "Number"}, {"field_id": "fldisfzdXV", "field_name": "Keywords-AI", "is_primary": false, "property": null, "type": 1, "ui_type": "Text"}], "page_token": "fldisfzdXV", "total": 29}, "msg": "success"}