# 数据库查询接口示例：
curl 'http://*************:28082/api/v1/kol/advanced-search' \
  -H 'Accept: application/json' \
  -H 'Content-Type: application/json' \
  --data-raw '{"conditions":[{"field":"kol_id","operator":"is_not_null","id":"xu9vb0rau"}],"project_code":"NOTM105","skip":0,"limit":1,"conjunction":"and"}' \
  --insecure

# 成功返回数据：
{
  "items": [
    {
        "kol_name": "...blizzy",
        "username": "…blizzy",
        "email": null,
        "bio": "münchen \n🇩🇪🇨🇭\n18",
        "account_link": "https://www.tiktok.com/@...blizzy",
        "followers_k": 231.455,
        "likes_k": null,
        "platform": "tiktok",
        "source": "TTONE",
        "slug": null,
        "creator_id": null,
        "mean_views_k": 145.39,
        "median_views_k": 80.33,
        "engagement_rate": null,
        "average_views_k": 89.653,
        "average_likes_k": null,
        "average_comments_k": null,
        "most_used_hashtags": null,
        "level": "Mid-tier 50k～500k",
        "keywords_ai": null,
        "kol_id": "TK_...blizzy",
        "created_at": "2025-06-19T06:11:27.352527Z",
        "updated_at": "2025-06-19T06:11:27.352528Z",
        "filter_names": [
            "TTone-cos+game-contenttag-30k-EU-3351"
        ],
        "tag_names": [
            "ACG"
        ],
        "project_codes": [
            "NOTM105"
        ]
    }
  ],
  "total": 76023,
  "page": 1,
  "size": 1,
  "pages": 76023
}

# 飞书批量添加接口文档：
https://open.feishu.cn/document/server-docs/docs/bitable-v1/app-table-record/batch_create
