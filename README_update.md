# 飞书数据更新脚本使用说明

## 概述

本脚本用于更新飞书多维表格中的Send Date和Send Status字段。脚本会：
1. 获取飞书中所有email不为空的记录
2. 通过KOL ID查询Send Data API获取最新的发送状态和时间
3. 将获取的数据更新回飞书对应记录

## 文件说明

- `update_send_data.py` - 主要的数据更新脚本
- `test_update_script.py` - 测试脚本，用于验证配置和API连接
- `task.md` - 任务清单和开发进度
- `README_update.md` - 本使用说明文档

## 环境要求

### Python依赖
```bash
pip install requests python-dotenv
```

### 配置文件
确保`.env`文件包含以下配置：

```env
# 飞书应用配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_APP_TOKEN=your_app_token
FEISHU_TABLE_ID=your_table_id

# API配置
DB_API_TIMEOUT=30

# 速率限制配置（秒）
FEISHU_RATE_LIMIT=0.1
DB_RATE_LIMIT=0.05

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1
```

## 使用步骤

### 1. 运行测试脚本

首次使用前，建议先运行测试脚本验证配置：

```bash
python test_update_script.py
```

测试脚本会验证：
- 配置文件完整性
- 飞书API连接和认证
- Send Data API连接
- 数据处理逻辑
- 单条记录更新流程（试运行）

### 2. 运行更新脚本

#### 试运行模式（推荐首次使用）
```bash
python update_send_data.py --dry-run
```

#### 正式运行
```bash
python update_send_data.py
```

## 功能特性

### 数据处理
- **自动分页**：支持飞书API的分页查询，处理大量数据
- **KOL ID处理**：自动为KOL ID添加"TK_"前缀用于API查询
- **时间转换**：将ISO格式时间转换为毫秒级时间戳
- **最新记录选择**：从多条发送记录中选择最新的一条

### 稳定性保障
- **访问令牌管理**：自动刷新过期的飞书访问令牌
- **重试机制**：指数退避重试策略，处理网络异常
- **速率限制**：避免API调用过于频繁
- **错误处理**：完整的异常捕获和错误日志
- **断点续传**：支持中断后重新运行

### 日志记录
- **详细日志**：记录所有操作和错误信息
- **进度报告**：实时显示处理进度
- **统计信息**：完成后显示处理统计

## 日志文件

- `update_send_data.log` - 更新脚本的运行日志
- 日志包含时间戳、级别和详细信息
- 可用于问题排查和运行监控

## 注意事项

### 数据安全
1. **试运行**：首次使用建议先用`--dry-run`模式测试
2. **备份**：重要数据建议先备份
3. **权限**：确保飞书应用有表格的读写权限

### 性能考虑
1. **批量处理**：脚本会自动分批处理，避免单次请求过大
2. **速率限制**：内置速率限制，避免触发API限制
3. **内存使用**：大量数据时注意内存使用情况

### API限制
1. **飞书API**：注意飞书API的调用频率限制
2. **Send Data API**：确保API服务正常运行
3. **网络连接**：确保网络连接稳定

## 故障排除

### 常见问题

1. **配置错误**
   ```
   错误：缺少必要的配置项
   解决：检查.env文件是否包含所有必要配置
   ```

2. **API连接失败**
   ```
   错误：飞书API连接失败
   解决：检查网络连接和应用权限
   ```

3. **访问令牌过期**
   ```
   错误：token过期
   解决：脚本会自动刷新，如持续失败检查应用配置
   ```

4. **数据格式错误**
   ```
   错误：时间转换失败
   解决：检查API返回的时间格式是否正确
   ```

### 调试建议

1. **查看日志**：检查详细的错误日志
2. **运行测试**：使用测试脚本验证各个组件
3. **分步调试**：可以修改脚本添加更多调试信息
4. **网络检查**：确认API服务可访问

## 扩展功能

### 自定义配置
可以通过修改`load_config()`函数添加更多配置选项：
- 自定义API超时时间
- 调整批处理大小
- 修改重试策略

### 数据过滤
可以修改飞书搜索条件，添加更多过滤条件：
- 按时间范围过滤
- 按特定字段值过滤
- 排除已更新的记录

### 监控集成
可以集成监控系统：
- 添加指标收集
- 集成告警系统
- 添加健康检查

## 联系支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行测试脚本验证配置
3. 检查网络连接和API服务状态
4. 提供完整的错误日志用于问题排查