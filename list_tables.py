#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列出飞书多维表格中的所有表
"""

import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def list_tables():
    """列出所有表格"""
    access_token = get_access_token()
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    result = response.json()
    
    print(f"响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result.get("code") == 0:
        tables = result.get("data", {}).get("items", [])
        print(f"\n找到 {len(tables)} 个表格:")
        for i, table in enumerate(tables):
            print(f"{i+1}. 表格ID: {table.get('table_id')}")
            print(f"   表格名: {table.get('name')}")
            print(f"   修订版本: {table.get('revision')}")
            print()

def main():
    try:
        list_tables()
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()