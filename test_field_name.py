#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用字段名而不是字段ID
"""

import os
import json
import requests
from dotenv import load_dotenv
import time

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def test_with_field_name():
    """使用字段名测试"""
    access_token = get_access_token()
    
    timestamp = int(time.time())
    test_kol_id = f"NAME_TEST_{timestamp}"
    
    # 使用字段名而不是字段ID
    test_record = {
        "fields": {
            "KOL ID": test_kol_id,  # 使用字段名
            "KOL Name": "Test KOL Name"  # 使用字段名
        }
    }
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records/batch_create"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {"records": [test_record]}
    
    print("使用字段名测试:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result.get("code") == 0

def test_different_api_method():
    """尝试不同的API方法 - 单条记录创建"""
    access_token = get_access_token()
    
    timestamp = int(time.time())
    test_kol_id = f"SINGLE_TEST_{timestamp}"
    
    # 单条记录格式
    test_data = {
        "fields": {
            "KOL ID": test_kol_id,
            "KOL Name": "Single Test KOL"
        }
    }
    
    # 使用单条记录创建API
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    print("\n使用单条记录API测试:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=test_data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result.get("code") == 0

def main():
    try:
        print("=== 测试1: 使用字段名 ===")
        success1 = test_with_field_name()
        
        print("\n" + "="*50)
        print("=== 测试2: 单条记录API ===")
        success2 = test_different_api_method()
        
        if success1 or success2:
            print("\n✅ 找到了正确的API使用方式!")
        else:
            print("\n❌ 仍然无法创建记录")
            
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()