#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查现有记录并测试写入
"""

import os
import json
import requests
from dotenv import load_dotenv
import uuid
import time

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def check_existing_records():
    """检查现有记录"""
    access_token = get_access_token()
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers, params={"page_size": 10})
    result = response.json()
    
    print("现有记录检查:")
    print(f"响应状态码: {response.status_code}")
    
    print("完整响应:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    if result.get("code") == 0:
        data = result.get("data", {})
        records = data.get("items") or []
        print(f"找到 {len(records)} 条记录")
        
        for i, record in enumerate(records):
            record_id = record.get("record_id")
            fields = record.get("fields", {})
            kol_id = fields.get("fldfX7JJlA", "N/A")
            kol_name = fields.get("fldtPzB7PT", "N/A")
            print(f"  记录 {i+1}: {record_id} - KOL ID: {kol_id}, 名称: {kol_name}")
            
        return records
    else:
        print(f"错误: {result.get('msg')}")
        return []

def test_create_with_existing_pattern():
    """根据现有记录模式创建测试记录"""
    access_token = get_access_token()
    
    # 使用时间戳创建唯一ID
    timestamp = int(time.time())
    unique_id = f"SYNC_TEST_{timestamp}"
    
    # 最简单的测试记录
    test_record = {
        "fields": {
            "fldfX7JJlA": unique_id,  # KOL ID
            "fldtPzB7PT": "Sync Test KOL",  # KOL Name
        }
    }
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records/batch_create"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {"records": [test_record]}
    
    print("\n测试创建记录:")
    print("发送数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result

def main():
    try:
        # 检查现有记录
        existing_records = check_existing_records()
        
        print("\n" + "="*60)
        
        # 测试创建记录
        result = test_create_with_existing_pattern()
        
        if result.get("code") == 0:
            print("\n✅ 创建成功! 表格和字段都正常")
        else:
            print(f"\n❌ 创建失败: {result.get('msg')}")
            
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()