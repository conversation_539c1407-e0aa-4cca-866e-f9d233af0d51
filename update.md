# 查询 email 不为空的所有数据
curl -i -X POST 'https://open.feishu.cn/open-apis/bitable/v1/apps/Ozj1bnsW8awu6lswMzgclzLfnIf/tables/tblMGQm7hGhvON2u/records/search?page_size=10&page_token=cGFnZVRva2VuOjI=' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer t-g10472dIOT3QBQW4QBZC4NS335UIY576NHDZ5Z7K' \
-d '{
	"automatic_fields": false,
	"filter": {
		"conditions": [
			{
				"field_name": "Email",
				"operator": "isNotEmpty",
				"value": []
			}
		],
		"conjunction": "and"
	}
}'
支持分页查询；通过page_token指定分页操作

# 数据返回示例
{
  "code": 0,
  "data": {
    "has_more": true,
    "items": [
      {
        "fields": {
          "Account link": {
            "link": "https://www.tiktok.com/@gigi_unbreakablebt",
            "text": "https://www.tiktok.com/@gigi_unbreakablebt",
            "type": "url"
          },
          "Average Views(K)": 43.227,
          "Bio": [
            {
              "text": "#UnBreakableBrooke\nBiz inquires; <EMAIL>\n💜Account by mom 💜",
              "type": "text"
            }
          ],
          "Email": [
            {
              "text": "<EMAIL>",
              "type": "text"
            }
          ],
          "Export Date": *************,
          "Filter": [
            {
              "text": "TTone-sport/family-contenttag-keyword search-US-2-3860, Modash-villain related-hashtags-10k-E-460",
              "type": "text"
            }
          ],
          "Followers(K)": 649.071,
          "KOL ID": [
            {
              "text": "gigi_unbreakablebt",
              "type": "text"
            }
          ],
          "KOL Name": [
            {
              "text": "Gigi_UnbreakableBT💜🩵",
              "type": "text"
            }
          ],
          "Level": "Mid-tier 50k～500k",
          "Mean Views(K)": 156.29,
          "Median Views(K)": 54.2,
          "Source": "TTONE",
          "Tag": [
            "Yoga&Pilates",
            "ACG"
          ]
        },
        "record_id": "recuPIv2zjLecv"
      }
    ],
    "page_token": "cGFnZVRva2VuOjUwMA==",   # 查询的时候带上这个参数，就会查询到下一页的数据
    "total": 4802
  },
  "msg": "success"
}

# 批量更新数据
curl -i -X POST 'https://open.feishu.cn/open-apis/bitable/v1/apps/Ozj1bnsW8awu6lswMzgclzLfnIf/tables/tblMGQm7hGhvON2u/records/batch_update' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer u-cTvs8q0B5cG96pcEl1Np6g5g5SYR0kaNX0w044.283xX' \
-d '{
	"records": [
		{
			"fields": {
				"Send Date": 1750204800000,
				"Send Status": "第2轮"
			},
			"record_id": "recuPIv2zjLecv"
		}
	]
}'

注意 Send Date 是毫秒级的时间戳
可参考下面代码获取
```python
import datetime

# 定义目标时间（注意：默认是本地时间，需指定时区或使用 UTC）
target_time = datetime.datetime(2025, 6, 18, 0, 0, 0)

# 转换为 UTC 时间（如果需要）
target_time_utc = target_time.replace(tzinfo=datetime.timezone.utc)

# 计算毫秒级时间戳
timestamp_ms = int(target_time_utc.timestamp() * 1000)
print(timestamp_ms)
```

# 通过API接口工具 kol_id 查询 Send Date 和 Send Status 的值
curl -X 'GET' \
  'http://54.84.111.234:8000/api/v1/send-data/kol/TK_gigi_unbreakablebt?page=1&size=10' \
  -H 'accept: application/json'
注意 kol id 在接口中需要添加 TK_ 前缀

# 成功返回值
{
  "items": [
    {
      "kol_id": "TK_gigi_unbreakablebt",
      "send_status": "",
      "platform": "tiktok",
      "send_date": "2025-06-26T15:01:29.278075Z",
      "export_date": "2025-06-26T15:01:29.278075Z",
      "app_code": "NOTM105",
      "template_id": "40526155",
      "read_status": false,
      "success": true,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "id": "e135dbc3-3468-448e-818e-33a1ea6bfecf",
      "created_at": "2025-06-26T15:02:14.155903Z",
      "updated_at": "2025-06-26T15:02:14.155903Z"
    },
    {
      "kol_id": "TK_gigi_unbreakablebt",
      "send_status": "",
      "platform": "tiktok",
      "send_date": "2025-06-26T15:00:26.535575Z",
      "export_date": "2025-06-26T15:00:26.535575Z",
      "app_code": "NOTM105",
      "template_id": "40526155",
      "read_status": false,
      "success": true,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "id": "93171503-713f-46c4-b754-7f766c4372e8",
      "created_at": "2025-06-26T15:01:12.209620Z",
      "updated_at": "2025-06-26T15:01:12.209620Z"
    },
    {
      "kol_id": "TK_gigi_unbreakablebt",
      "send_status": "",
      "platform": "tiktok",
      "send_date": "2025-06-26T14:57:37.416117Z",
      "export_date": "2025-06-26T14:57:37.416117Z",
      "app_code": "",
      "template_id": "40526155",
      "read_status": false,
      "success": true,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "id": "35b697e5-9180-49b9-8e84-2bf2e8e76a69",
      "created_at": "2025-06-26T14:58:19.004046Z",
      "updated_at": "2025-06-26T14:58:19.004046Z"
    },
    {
      "kol_id": "TK_gigi_unbreakablebt",
      "send_status": "Round No.13",
      "platform": "tiktok",
      "send_date": "2025-06-24T23:49:22.111039Z",
      "export_date": "2025-06-24T23:49:22.111039Z",
      "app_code": "NOTM105",
      "template_id": "40355323",
      "read_status": false,
      "success": true,
      "from_email": "<EMAIL>",
      "to_email": "<EMAIL>",
      "id": "bb8da737-d23e-4920-8a6f-c7e1d59241c8",
      "created_at": "2025-06-24T23:49:40.881898Z",
      "updated_at": "2025-06-24T23:49:40.881898Z"
    }
  ],
  "total": 4,
  "page": 1,
  "size": 10,
  "pages": 1
}
注意 send_date 需要转换为毫秒级时间戳
注意 send_status 和 send_date 有多条记录，取时间最新的那条记录
