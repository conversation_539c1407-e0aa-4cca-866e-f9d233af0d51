#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查目标表格字段信息
"""

import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def check_table_fields(table_id, table_name):
    """检查表格字段"""
    access_token = get_access_token()
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{table_id}/fields"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    result = response.json()
    
    print(f"表格: {table_name} (ID: {table_id})")
    print(f"响应状态码: {response.status_code}")
    
    if result.get("code") == 0:
        fields = result.get("data", {}).get("items", [])
        print(f"字段数量: {len(fields)}")
        for field in fields:
            print(f"  - {field.get('field_id')}: {field.get('field_name')} ({field.get('ui_type')})")
    else:
        print(f"错误: {result.get('msg')}")
    
    print("-" * 60)
    return result

def main():
    try:
        # 检查目标表
        target_table_id = os.getenv("FEISHU_TABLE_ID")
        check_table_fields(target_table_id, "NOTM105 表1（测试）")
        
        # 检查一个已知有数据的表作为参考
        check_table_fields("tbl5BetO1a0GZgGk", "OOG120表2（部分可二追）")
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()