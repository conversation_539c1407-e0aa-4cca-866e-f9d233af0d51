#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新脚本的基本功能
验证配置、API连接和数据处理逻辑
"""

import os
import sys
import json
import logging
from dotenv import load_dotenv
from update_send_data import load_config, FeishuUpdateAPI, SendDataAPI, DataUpdater

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config():
    """测试配置加载"""
    logger.info("=== 测试配置加载 ===")
    try:
        config = load_config()
        
        # 检查必要配置
        required_fields = [
            'feishu_app_id', 'feishu_app_secret', 'feishu_app_token', 
            'feishu_table_id', 'send_data_api_url'
        ]
        
        missing_fields = []
        for field in required_fields:
            value = getattr(config, field)
            if not value:
                missing_fields.append(field)
            else:
                # 只显示前几个字符，保护敏感信息
                display_value = value[:10] + "..." if len(value) > 10 else value
                logger.info(f"{field}: {display_value}")
        
        if missing_fields:
            logger.error(f"缺少配置: {missing_fields}")
            return False
        
        logger.info("✅ 配置加载成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置加载失败: {str(e)}")
        return False

def test_feishu_api():
    """测试飞书API连接"""
    logger.info("=== 测试飞书API连接 ===")
    try:
        config = load_config()
        feishu_api = FeishuUpdateAPI(config)
        
        # 测试获取访问令牌
        token = feishu_api.get_access_token()
        logger.info(f"访问令牌: {token[:20]}...")
        
        # 测试搜索记录（只获取第一页，限制数量）
        result = feishu_api.search_records_with_email()
        data = result.get("data", {})
        total = data.get("total", 0)
        items_count = len(data.get("items", []))
        
        logger.info(f"总记录数: {total}")
        logger.info(f"当前页记录数: {items_count}")
        
        if items_count > 0:
            # 显示第一条记录的基本信息
            first_record = data["items"][0]
            record_id = first_record.get("record_id")
            fields = first_record.get("fields", {})
            kol_id = fields.get("KOL ID")
            email = fields.get("Email")
            
            logger.info(f"示例记录ID: {record_id}")
            logger.info(f"示例KOL ID: {kol_id}")
            logger.info(f"示例Email: {email}")
        
        logger.info("✅ 飞书API连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 飞书API连接失败: {str(e)}")
        return False

def test_send_data_api():
    """测试Send Data API连接"""
    logger.info("=== 测试Send Data API连接 ===")
    try:
        config = load_config()
        send_api = SendDataAPI(config)
        
        # 使用文档中的示例KOL ID进行测试
        test_kol_id = "gigi_unbreakablebt"
        
        logger.info(f"测试KOL ID: {test_kol_id}")
        
        send_data = send_api.get_send_data(test_kol_id)
        
        if send_data:
            logger.info(f"获取到发送数据:")
            logger.info(f"  KOL ID: {send_data.get('kol_id')}")
            logger.info(f"  Send Status: {send_data.get('send_status')}")
            logger.info(f"  Send Date: {send_data.get('send_date')}")
            logger.info(f"  Created At: {send_data.get('created_at')}")
            logger.info(f"  Success: {send_data.get('success')}")
        else:
            logger.warning(f"KOL {test_kol_id} 没有发送数据")
        
        logger.info("✅ Send Data API连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Send Data API连接失败: {str(e)}")
        return False

def test_data_processing():
    """测试数据处理逻辑"""
    logger.info("=== 测试数据处理逻辑 ===")
    try:
        config = load_config()
        updater = DataUpdater(config)
        
        # 测试时间转换
        test_iso_time = "2025-06-26T15:01:29.278075Z"
        timestamp_ms = updater.convert_iso_to_timestamp_ms(test_iso_time)
        logger.info(f"时间转换测试: {test_iso_time} -> {timestamp_ms}")
        
        # 测试KOL ID提取
        test_record = {
            "fields": {
                "KOL ID": [
                    {
                        "text": "test_kol_id",
                        "type": "text"
                    }
                ]
            }
        }
        
        kol_id = updater.extract_kol_id(test_record)
        logger.info(f"KOL ID提取测试: {kol_id}")
        
        logger.info("✅ 数据处理逻辑测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据处理逻辑测试失败: {str(e)}")
        return False

def test_single_record_update():
    """测试单条记录更新（试运行）"""
    logger.info("=== 测试单条记录更新（试运行） ===")
    try:
        config = load_config()
        feishu_api = FeishuUpdateAPI(config)
        send_api = SendDataAPI(config)
        updater = DataUpdater(config)
        
        # 获取第一条有email的记录
        result = feishu_api.search_records_with_email()
        data = result.get("data", {})
        items = data.get("items", [])
        
        if not items:
            logger.warning("没有找到有email的记录")
            return False
        
        # 取第一条记录进行测试
        test_record = items[0]
        record_id = test_record.get("record_id")
        
        # 提取KOL ID
        kol_id = updater.extract_kol_id(test_record)
        if not kol_id:
            logger.warning(f"记录 {record_id} 没有有效的KOL ID")
            return False
        
        logger.info(f"测试记录: {record_id}, KOL ID: {kol_id}")
        
        # 获取发送数据
        send_data = send_api.get_send_data(kol_id)
        if not send_data:
            logger.info(f"KOL {kol_id} 没有发送数据")
            return True  # 这不算失败
        
        # 准备更新字段
        update_fields = {}
        
        # 处理Send Date
        send_date = send_data.get("send_date")
        if send_date:
            timestamp_ms = updater.convert_iso_to_timestamp_ms(send_date)
            if timestamp_ms > 0:
                update_fields["Send Date"] = timestamp_ms
        
        # 处理Send Status
        send_status = send_data.get("send_status")
        if send_status:
            update_fields["Send Status"] = send_status
        
        logger.info(f"准备更新的字段: {json.dumps(update_fields, indent=2, ensure_ascii=False)}")
        
        # 注意：这里是试运行，不实际更新
        logger.info("⚠️  试运行模式：不实际更新数据")
        logger.info("✅ 单条记录更新测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 单条记录更新测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("开始测试更新脚本功能")
    
    tests = [
        ("配置加载", test_config),
        ("飞书API连接", test_feishu_api),
        ("Send Data API连接", test_send_data_api),
        ("数据处理逻辑", test_data_processing),
        ("单条记录更新", test_single_record_update)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！更新脚本准备就绪")
        return 0
    else:
        logger.error(f"⚠️  有 {total - passed} 项测试失败，请检查配置和网络连接")
        return 1

if __name__ == "__main__":
    sys.exit(main())