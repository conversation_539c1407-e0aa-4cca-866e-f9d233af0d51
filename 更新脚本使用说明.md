# 飞书数据更新脚本使用说明

## 概述

`update_send_data.py` 是一个用于更新飞书多维表格中Send Date和Send Status字段的脚本。脚本会：

1. **获取数据**：从飞书获取所有email不为空的记录（支持分页）
2. **查询API**：通过KOL ID查询Send Data API获取最新的发送状态和时间
3. **批量更新**：将获取的数据批量更新回飞书对应记录

## 主要功能特性

### 🔄 数据处理
- **自动分页**：支持飞书API的分页查询，处理大量数据
- **KOL ID处理**：自动清理特殊字符和emoji，添加"TK_"前缀
- **时间转换**：将ISO格式时间转换为毫秒级时间戳
- **最新记录选择**：从多条发送记录中选择created_at最新的一条

### 🛡️ 稳定性保障
- **访问令牌管理**：自动刷新过期的飞书访问令牌
- **重试机制**：指数退避重试策略，处理网络异常
- **速率限制**：避免API调用过于频繁
- **错误处理**：完整的异常捕获和错误日志
- **批量处理**：支持批量更新，提高效率

### 📊 监控和日志
- **详细日志**：记录所有操作和错误信息到文件和控制台
- **进度报告**：实时显示处理进度
- **统计信息**：完成后显示详细的处理统计

## 环境要求

### Python依赖
```bash
pip install requests python-dotenv
```

### 配置文件
确保`.env`文件包含以下配置：

```env
# 飞书应用配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_APP_TOKEN=your_app_token
FEISHU_TABLE_ID=your_table_id

# Send Data API配置
SEND_DATA_API_URL=http://*************:8000/api/v1/send-data/kol
SEND_DATA_API_TIMEOUT=30
SEND_DATA_RATE_LIMIT=0.05

# 飞书分页配置
FEISHU_PAGE_SIZE=100
FEISHU_BATCH_SIZE=500

# 速率限制配置（秒）
FEISHU_RATE_LIMIT=0.1

# 重试配置
MAX_RETRIES=3
RETRY_DELAY=1
```

## 使用方法

### 1. 试运行模式（推荐首次使用）
```bash
python3 update_send_data.py --dry-run
```

试运行模式会：
- 执行所有查询和数据处理逻辑
- 显示将要更新的数据
- **不会实际更新飞书数据**
- 用于验证脚本功能和数据正确性

### 2. 正式运行
```bash
python3 update_send_data.py
```

正式运行会：
- 执行完整的数据更新流程
- 实际更新飞书表格数据
- 显示详细的处理统计

### 3. 查看帮助
```bash
python3 update_send_data.py --help
```

## 输出示例

### 运行过程日志
```
2025-07-02 14:47:12,138 - INFO - 开始更新Send Date和Send Status数据 - 模式: 试运行
2025-07-02 14:47:14,080 - INFO - 成功获取飞书访问令牌
2025-07-02 14:47:16,366 - INFO - 处理当前页 100 条记录
2025-07-02 14:47:25,600 - INFO - 已处理 100 条记录，准备更新 25 条记录
```

### 完成统计
```
更新统计:
- 处理记录数: 4802
- 成功更新数: 1250
- 失败记录数: 0
- 无数据记录数: 3552
- 总耗时: 125.45秒
```

## 日志文件

- **文件位置**：`update_send_data.log`
- **内容**：包含所有操作日志、错误信息和调试信息
- **用途**：问题排查和运行监控

## 注意事项

### 数据安全
1. **试运行**：首次使用建议先用`--dry-run`模式测试
2. **备份**：重要数据建议先备份
3. **权限**：确保飞书应用有表格的读写权限

### 性能优化
1. **批量大小**：可通过`FEISHU_BATCH_SIZE`调整批量更新大小
2. **速率限制**：可通过`FEISHU_RATE_LIMIT`调整API调用频率
3. **分页大小**：可通过`FEISHU_PAGE_SIZE`调整每页获取的记录数

### 错误处理
1. **网络异常**：脚本会自动重试，可通过`MAX_RETRIES`调整重试次数
2. **API限制**：遇到速率限制时会自动等待
3. **数据异常**：无效的KOL ID或时间格式会被跳过并记录日志

## 故障排除

### 常见问题

1. **配置错误**
   - 检查`.env`文件是否存在且配置正确
   - 验证飞书应用权限

2. **网络连接问题**
   - 检查Send Data API是否可访问
   - 验证飞书API连接

3. **数据格式问题**
   - 查看日志文件了解具体错误
   - 检查KOL ID格式是否正确

### 调试模式
可以修改日志级别为DEBUG来获取更详细的信息：
```python
logging.basicConfig(level=logging.DEBUG, ...)
```

## 扩展功能

脚本设计为模块化结构，可以轻松扩展：

1. **添加新的数据源**：扩展SendDataAPI类
2. **支持其他字段**：修改DataUpdater类的字段处理逻辑
3. **自定义过滤条件**：修改FeishuUpdateAPI类的搜索条件
4. **添加数据验证**：在更新前添加数据验证逻辑

## 技术架构

- **FeishuUpdateAPI**：处理飞书API操作
- **SendDataAPI**：处理Send Data API查询
- **DataUpdater**：协调数据更新流程
- **UpdateConfig**：管理配置参数

脚本采用面向对象设计，便于维护和扩展。
