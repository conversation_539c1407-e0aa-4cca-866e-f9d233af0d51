#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试偏移量同步功能
"""

import os
import sys
from sync_data import load_config, DataSynchronizer

def test_offset_sync():
    """测试偏移量同步功能"""
    print("=== 测试偏移量同步功能 ===")
    
    try:
        # 加载配置
        config = load_config()
        print(f"默认配置 - 起始偏移量: {config.start_offset}, 起始页数: {config.start_page}")
        print(f"批次大小: {config.db_batch_size}")
        
        # 创建同步器
        synchronizer = DataSynchronizer(config)
        
        # 测试1：使用偏移量
        print("\n--- 测试1：使用偏移量 100 ---")
        stats1 = synchronizer.sync_data(
            start_offset=100,
            max_records=10  # 只同步10条记录用于测试
        )
        print(f"测试1结果: 起始偏移量={stats1.get('start_offset')}, 获取={stats1['total_fetched']}, 同步={stats1['total_synced']}")
        
        # 测试2：使用页数
        print("\n--- 测试2：使用页数 2 ---")
        stats2 = synchronizer.sync_data(
            start_page=2,
            max_records=10  # 只同步10条记录用于测试
        )
        expected_offset = (2 - 1) * config.db_batch_size
        print(f"测试2结果: 起始偏移量={stats2.get('start_offset')} (期望={expected_offset}), 获取={stats2['total_fetched']}, 同步={stats2['total_synced']}")
        
        # 测试3：同时指定偏移量和页数（偏移量优先）
        print("\n--- 测试3：同时指定偏移量200和页数5（偏移量优先） ---")
        stats3 = synchronizer.sync_data(
            start_offset=200,
            start_page=5,
            max_records=10
        )
        print(f"测试3结果: 起始偏移量={stats3.get('start_offset')} (应该是200), 获取={stats3['total_fetched']}, 同步={stats3['total_synced']}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_calculation():
    """测试计算逻辑"""
    print("\n=== 测试计算逻辑 ===")
    
    config = load_config()
    batch_size = config.db_batch_size
    
    # 测试页数到偏移量的转换
    test_cases = [
        (1, 0),  # 第1页 -> 偏移量0
        (2, batch_size),  # 第2页 -> 偏移量batch_size
        (201, 200 * batch_size),  # 第201页 -> 偏移量200*batch_size
    ]
    
    for page, expected_offset in test_cases:
        actual_offset = (page - 1) * batch_size
        print(f"页数 {page} -> 偏移量 {actual_offset} (期望: {expected_offset}) {'✓' if actual_offset == expected_offset else '✗'}")
    
    # 测试偏移量到页数的转换
    test_offsets = [0, batch_size, 20000, 40000]
    for offset in test_offsets:
        page = (offset // batch_size) + 1
        print(f"偏移量 {offset} -> 页数 {page}")

if __name__ == "__main__":
    # 检查是否有必要的环境变量
    required_env = ['FEISHU_APP_ID', 'FEISHU_APP_SECRET', 'FEISHU_APP_TOKEN', 'FEISHU_TABLE_ID', 'DB_API_BASE_URL']
    missing_env = [env for env in required_env if not os.getenv(env)]
    
    if missing_env:
        print(f"警告: 缺少环境变量 {missing_env}，将只进行计算逻辑测试")
        test_calculation()
    else:
        test_calculation()
        test_offset_sync()