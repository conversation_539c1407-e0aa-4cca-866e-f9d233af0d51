# 数据同步脚本

该脚本用于从API获取KOL数据并同步到飞书多维表格。

## 功能特性

- **批量数据获取**: 从API批量获取数据，支持分页
- **数据格式转换**: 自动将API数据格式转换为飞书表格格式
- **批量写入**: 支持批量写入飞书表格，提高效率
- **错误重试**: 内置重试机制，确保同步稳定性
- **速率限制**: 支持API调用速率限制，避免频率过高
- **数据限制**: 单表最多写入20000条数据
- **详细日志**: 完整的日志记录，便于监控和调试

## 环境要求

- Python 3.7+
- 依赖包：见 `requirements.txt`

## 安装

```bash
pip install -r requirements.txt
```

## 配置

配置文件已在 `.env` 中提供，包含：

- 飞书应用配置 (APP_ID, APP_SECRET, APP_TOKEN, TABLE_ID)
- 数据库API配置
- 批处理大小配置
- 速率限制配置
- 重试配置

## 使用方法

### 基本使用

```bash
python sync_data.py
```

### 边界情况处理

脚本已考虑以下边界情况：

1. **网络异常**: 内置重试机制，最多重试3次
2. **API限制**: 支持速率限制，避免请求过于频繁
3. **数据格式异常**: 对每个字段进行类型检查和转换
4. **飞书Token过期**: 自动刷新访问令牌
5. **批量写入失败**: 自动降级为单条写入
6. **数据量限制**: 单表最多20000条数据
7. **空数据处理**: 优雅处理空值和空数组

## 日志

- 日志文件：`sync_data.log`
- 控制台输出：实时显示同步进度
- 详细统计：同步完成后显示统计信息

## 数据映射

| 飞书字段 | API字段 | 类型 |
|---------|---------|------|
| KOL ID | kol_id | 文本 |
| Email | email | 文本 |
| Account link | account_link | 链接 |
| KOL Name | kol_name | 文本 |
| Bio | bio | 文本 |
| Source | source | 单选 |
| Filter | filter_names | 文本 |
| Followers(K) | followers_k | 数字 |
| Likes(K) | likes_k | 数字 |
| Mean Views(K) | mean_views_k | 数字 |
| Median Views(K) | median_views_k | 数字 |
| Keywords-AI | keywords_ai | 文本 |