# 数据同步脚本使用说明

## 功能概述

本脚本支持从API获取数据并同步到飞书表格，现已支持指定起始位置的增量同步功能。

## 新增功能

### 1. 起始偏移量同步
可以指定从第几条记录开始同步，适用于大数据量的分批处理。

### 2. 起始页数同步
可以指定从第几页开始同步，页数从1开始计算。

## 使用方法

### 基本用法
```bash
# 从头开始同步（默认行为）
python sync_data.py
```

### 指定起始偏移量
```bash
# 从第20001条记录开始同步
python sync_data.py --start-offset 20000

# 从第40001条记录开始同步
python sync_data.py --start-offset 40000
```

### 指定起始页数
```bash
# 从第201页开始同步（假设每页100条记录）
python sync_data.py --start-page 201

# 从第401页开始同步
python sync_data.py --start-page 401
```

### 组合参数使用
```bash
# 指定起始偏移量和最大记录数
python sync_data.py --start-offset 20000 --max-records 20000

# 指定起始页数、最大记录数和项目代码
python sync_data.py --start-page 201 --max-records 20000 --project-code NOTM105
```

## 参数说明

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `--start-offset` | int | 起始偏移量（从第几条记录开始） | `--start-offset 20000` |
| `--start-page` | int | 起始页数（从第几页开始，页数从1开始） | `--start-page 201` |
| `--max-records` | int | 最大同步记录数 | `--max-records 20000` |
| `--project-code` | str | 项目代码 | `--project-code NOTM105` |

## 环境变量配置

也可以通过环境变量设置默认值：

```bash
# 在.env文件中添加
START_OFFSET=0
START_PAGE=1
```

## 实际使用场景

### 场景1：完成0-20000数据同步后，继续同步20001-40000
```bash
# 方法1：使用偏移量
python sync_data.py --start-offset 20000 --max-records 20000

# 方法2：使用页数（假设每页100条记录）
python sync_data.py --start-page 201 --max-records 20000
```

### 场景2：完成0-40000数据同步后，继续同步40001-60000
```bash
# 方法1：使用偏移量
python sync_data.py --start-offset 40000 --max-records 20000

# 方法2：使用页数
python sync_data.py --start-page 401 --max-records 20000
```

## 优先级说明

1. 命令行参数优先级最高
2. 如果同时指定了 `--start-offset` 和 `--start-page`，优先使用 `--start-offset`
3. 如果都没有指定，使用环境变量或配置文件中的默认值

## 输出信息

脚本执行完成后会显示：
- 起始偏移量
- 同步统计信息
- 下次同步建议（包含具体的偏移量和页数）

示例输出：
```
同步统计:
- 起始偏移量: 20000
- 获取记录数: 20000
- 成功同步数: 19850
- 失败记录数: 150
- 处理批次数: 200
- 总耗时: 1234.56秒

下次同步建议:
- 使用偏移量: --start-offset 39850
- 或使用页数: --start-page 399
```

## 注意事项

1. 起始偏移量不能超过可用数据总数
2. 页数计算基于配置的批次大小（DB_BATCH_SIZE）
3. 建议在大数据量同步时使用偏移量方式，更加精确
4. 每次同步完成后，脚本会自动计算并提示下次同步的起始位置