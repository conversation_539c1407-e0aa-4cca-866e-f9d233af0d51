#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试已知工作正常的表格
"""

import os
import json
import requests
from dotenv import load_dotenv
import time

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def test_working_table():
    """测试一个已知有数据的表格"""
    access_token = get_access_token()
    
    # 使用 OOG120表2 作为测试
    working_table_id = "tbl5BetO1a0GZgGk"
    
    # 先获取表格字段
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{working_table_id}/fields"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    result = response.json()
    
    print("工作表格字段信息:")
    if result.get("code") == 0:
        fields = result.get("data", {}).get("items", [])
        print(f"字段数量: {len(fields)}")
        
        # 找到主键字段
        primary_field = None
        for field in fields[:5]:  # 只看前5个字段
            print(f"  - {field.get('field_id')}: {field.get('field_name')} (主键: {field.get('is_primary', False)})")
            if field.get('is_primary'):
                primary_field = field
        
        if primary_field:
            print(f"\n主键字段: {primary_field.get('field_id')} - {primary_field.get('field_name')}")
            
            # 尝试创建一条记录
            timestamp = int(time.time())
            test_kol_id = f"API_TEST_{timestamp}"
            
            test_record = {
                "fields": {
                    primary_field.get('field_id'): test_kol_id
                }
            }
            
            print(f"\n尝试创建测试记录:")
            print(json.dumps({"records": [test_record]}, indent=2, ensure_ascii=False))
            
            # 创建记录
            create_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{working_table_id}/records/batch_create"
            
            create_response = requests.post(create_url, headers=headers, json={"records": [test_record]})
            create_result = create_response.json()
            
            print(f"\n创建结果 (状态码: {create_response.status_code}):")
            print(json.dumps(create_result, indent=2, ensure_ascii=False))
            
            return create_result.get("code") == 0
        else:
            print("未找到主键字段")
            return False
    else:
        print(f"获取字段失败: {result.get('msg')}")
        return False

def main():
    try:
        success = test_working_table()
        if success:
            print("\n✅ 工作表格测试成功! API和权限都正常")
        else:
            print("\n❌ 工作表格测试失败")
            
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()