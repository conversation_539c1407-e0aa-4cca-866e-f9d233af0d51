#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单条记录写入脚本 - 调试字段映射问题
"""

import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def get_sample_api_data():
    """获取示例API数据"""
    payload = {
        "conditions": [{"field": "kol_id", "operator": "is_not_null", "id": "xu9vb0rau"}],
        "project_code": "NOTM105",
        "skip": 0,
        "limit": 1,
        "conjunction": "and"
    }
    
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    response = requests.post(
        "http://*************:28082/api/v1/kol/advanced-search",
        headers=headers,
        json=payload,
        verify=False
    )
    
    result = response.json()
    return result.get("items", [])[0] if result.get("items") else None

def test_minimal_record():
    """测试最小字段集合"""
    access_token = get_access_token()
    
    # 最小字段集合 - 只包含必填字段和确定存在的字段
    minimal_record = {
        "fields": {
            "fldfX7JJlA": "TEST_KOL_001",  # KOL ID (主键)
        }
    }
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records/batch_create"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {"records": [minimal_record]}
    
    print("发送数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result

def test_api_record_mapping():
    """测试API数据映射"""
    api_record = get_sample_api_data()
    if not api_record:
        print("无法获取API数据")
        return
    
    print("API原始数据:")
    print(json.dumps(api_record, indent=2, ensure_ascii=False))
    
    # 简化的字段映射 - 只映射确定的字段
    safe_mapping = {
        "fldfX7JJlA": "kol_id",     # KOL ID
        "fldtPzB7PT": "kol_name",   # KOL Name  
        "fld9z4F71u": "email",      # Email
        "fldgCMdgRT": "bio",        # Bio
    }
    
    mapped_record = {"fields": {}}
    
    for field_id, api_field in safe_mapping.items():
        value = api_record.get(api_field)
        if value is not None:
            mapped_record["fields"][field_id] = str(value) if value else ""
    
    print(f"\n映射后的记录:")
    print(json.dumps(mapped_record, indent=2, ensure_ascii=False))
    
    # 测试发送
    access_token = get_access_token()
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records/batch_create"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {"records": [mapped_record]}
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

def main():
    try:
        print("=== 测试1: 最小记录 ===")
        test_minimal_record()
        
        print("\n" + "="*50)
        print("=== 测试2: API数据映射 ===")
        test_api_record_mapping()
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()