#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本 - 获取飞书表格的实际字段信息
"""

import os
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def get_table_fields():
    """获取表格字段信息"""
    access_token = get_access_token()
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/fields"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(url, headers=headers)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取字段失败: {result.get('msg')}")
    
    return result["data"]["items"]

def main():
    try:
        fields = get_table_fields()
        
        print("飞书表格字段信息:")
        print("=" * 80)
        
        for field in fields:
            field_id = field.get("field_id")
            field_name = field.get("field_name")
            field_type = field.get("type")
            ui_type = field.get("ui_type")
            is_primary = field.get("is_primary", False)
            
            print(f"字段ID: {field_id}")
            print(f"字段名: {field_name}")
            print(f"类型: {field_type} ({ui_type})")
            print(f"主键: {is_primary}")
            
            if field.get("property"):
                print(f"属性: {json.dumps(field['property'], ensure_ascii=False, indent=2)}")
            
            print("-" * 40)
        
        # 生成字段映射代码
        print("\n建议的字段映射:")
        print("FIELD_MAPPING = {")
        
        for field in fields:
            field_id = field.get("field_id")
            field_name = field.get("field_name")
            
            # 根据字段名推荐API字段
            name_lower = field_name.lower()
            if "kol id" in name_lower:
                api_field = "kol_id"
            elif "email" in name_lower:
                api_field = "email"
            elif "account link" in name_lower:
                api_field = "account_link"
            elif "kol name" in name_lower:
                api_field = "kol_name"
            elif "bio" in name_lower:
                api_field = "bio"
            elif "source" in name_lower:
                api_field = "source"
            elif "filter" in name_lower:
                api_field = "filter_names"
            elif "followers" in name_lower:
                api_field = "followers_k"
            elif "likes" in name_lower:
                api_field = "likes_k"
            elif "mean views" in name_lower:
                api_field = "mean_views_k"
            elif "median views" in name_lower:
                api_field = "median_views_k"
            elif "keywords" in name_lower:
                api_field = "keywords_ai"
            else:
                api_field = f"# {field_name}"
            
            # 确定字段类型
            ui_type = field.get("ui_type", "")
            if ui_type == "Text":
                field_type = "text"
            elif ui_type == "Number":
                field_type = "number"
            elif ui_type == "Url":
                field_type = "url"
            elif ui_type in ["SingleSelect", "MultiSelect"]:
                field_type = "select"
            elif ui_type == "DateTime":
                field_type = "datetime"
            else:
                field_type = "text"
            
            if not api_field.startswith("#"):
                print(f'    "{field_id}": ("{api_field}", "{field_type}"),  # {field_name}')
            else:
                print(f'    # "{field_id}": ("", "{field_type}"),  # {field_name}')
        
        print("}")
        
    except Exception as e:
        print(f"错误: {str(e)}")

if __name__ == "__main__":
    main()