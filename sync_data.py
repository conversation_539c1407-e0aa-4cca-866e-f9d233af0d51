#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据同步脚本 - 从API获取数据并同步到飞书表格
支持批量处理、错误重试、速率限制等稳定性保障
"""

import os
import sys
import json
import time
import logging
import requests
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
from dotenv import load_dotenv
import traceback

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync_data.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class SyncConfig:
    """同步配置类"""
    # 飞书配置
    feishu_app_id: str
    feishu_app_secret: str
    feishu_app_token: str
    feishu_table_id: str
    
    # API配置
    db_api_url: str
    db_api_timeout: int
    
    # 批处理配置
    db_batch_size: int
    feishu_batch_size: int
    max_records_per_table: int
    
    # 速率限制
    feishu_rate_limit: float
    db_rate_limit: float
    
    # 重试配置
    max_retries: int
    retry_delay: float
    
    # 并发配置
    max_workers: int
    
    # 项目配置
    default_project_code: str
    
    # 同步起始位置配置
    start_offset: int = 0  # 起始偏移量（从第几条记录开始）
    start_page: int = 1    # 起始页数（从第几页开始，页数从1开始）

class FeishuAPI:
    """飞书API客户端"""
    
    def __init__(self, config: SyncConfig):
        self.config = config
        self.access_token: Optional[str] = None
        self.token_expires_at: float = 0
        self.session = requests.Session()
        
    def get_access_token(self) -> str:
        """获取或刷新访问令牌"""
        current_time = time.time()
        
        if self.access_token and current_time < self.token_expires_at:
            return self.access_token
            
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        data = {
            "app_id": self.config.feishu_app_id,
            "app_secret": self.config.feishu_app_secret
        }
        
        response = self.session.post(url, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result.get("code") != 0:
            raise Exception(f"获取访问令牌失败: {result.get('msg')}")
            
        self.access_token = result["tenant_access_token"]
        self.token_expires_at = current_time + result["expire"] - 300  # 提前5分钟刷新
        
        logger.info("成功获取飞书访问令牌")
        return self.access_token
    
    def batch_create_records(self, records: List[Dict]) -> Dict:
        """批量创建记录"""
        if not records:
            return {"code": 0, "data": {"records": []}}
            
        if len(records) > self.config.feishu_batch_size:
            raise ValueError(f"批量记录数超出限制: {len(records)} > {self.config.feishu_batch_size}")
        
        access_token = self.get_access_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.config.feishu_app_token}/tables/{self.config.feishu_table_id}/records/batch_create"
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        data = {"records": records}
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(url, headers=headers, json=data, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                if result.get("code") != 0:
                    if result.get("code") == 99991663:  # token过期
                        self.access_token = None
                        access_token = self.get_access_token()
                        headers["Authorization"] = f"Bearer {access_token}"
                        continue
                    raise Exception(f"批量创建记录失败: {result.get('msg')}")
                
                logger.info(f"成功创建 {len(records)} 条记录")
                return result
                
            except Exception as e:
                logger.warning(f"批量创建记录失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))
        
        # 速率限制
        time.sleep(self.config.feishu_rate_limit)

class DataFetcher:
    """数据获取器"""
    
    def __init__(self, config: SyncConfig):
        self.config = config
        self.session = requests.Session()
    
    def fetch_data_batch(self, skip: int, limit: int, project_code: str = None) -> Dict:
        """批量获取数据"""
        project_code = project_code or self.config.default_project_code
        
        payload = {
            "conditions": [{"field": "kol_id", "operator": "is_not_null", "id": "xu9vb0rau"}],
            "project_code": project_code,
            "skip": skip,
            "limit": limit,
            "conjunction": "and"
        }
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(
                    self.config.db_api_url,
                    headers=headers,
                    json=payload,
                    timeout=self.config.db_api_timeout,
                    verify=False
                )
                response.raise_for_status()
                
                result = response.json()
                logger.info(f"成功获取数据: skip={skip}, limit={limit}, total={result.get('total', 0)}")
                return result
                
            except Exception as e:
                logger.warning(f"获取数据失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))
        
        # 速率限制
        time.sleep(self.config.db_rate_limit)

class DataMapper:
    """数据映射器 - 将API数据转换为飞书格式"""
    
    # 字段映射配置 - 使用字段名而不是字段ID
    FIELD_MAPPING = {
        "KOL ID": ("kol_name", "text"),                    # KOL ID
        "Email": ("email", "text"),                      # Email
        "Account link": ("account_link", "url"),         # Account link
        "KOL Name": ("username", "text"),               # KOL Name
        "Bio": ("bio", "text"),                         # Bio
        "Source": ("source", "select"),                 # Source
        "Filter": ("filter_names", "text"),             # Filter
        "Followers(K)": ("followers_k", "number"),      # Followers(K)
        "Likes(K)": ("likes_k", "number"),              # Likes(K)
        "Mean Views(K)": ("mean_views_k", "number"),    # Mean Views(K)
        "Median Views(K)": ("median_views_k", "number"), # Median Views(K)
        "Keywords-AI": ("keywords_ai", "text"),         # Keywords-AI
        "Level": ("level", "select"),                   # Level
        "Engagement Rate(%)": ("engagement_rate", "number"), # Engagement Rate(%)
        "Average Views(K)": ("average_views_k", "number"), # Average Views(K)
        "Average Likes(K)": ("average_likes_k", "number"), # Average Likes(K)
        "Average Comments(K)": ("average_comments_k", "number"), # Average Comments(K)
        "Most used hashtags": ("most_used_hashtags", "text"), # Most used hashtags
        "Slug": ("slug", "text"),                       # Slug
        "Creator ID": ("creator_id", "text"),           # Creator ID
        "Tag": ("tag_names", "multiselect"),            # Tag
    }
    
    # 来源选项映射
    SOURCE_OPTIONS = {
        "TTONE": "opt45iCdi3",  # 映射到 Collabstr (如果没有TTONE选项)
        "Manual": "optWy0UYXu",
        "Collabstr": "opt45iCdi3",
        "Creable": "opt9KMQetm",
        "Heepsy": "opt1115032617",
        "Modash": "opt962695875",
    }
    
    # Level选项映射
    LEVEL_OPTIONS = {
        "Nano 1k～10k": "optgzYkasF",
        "Micro 10k～50k": "optdUw4abJ",
        "Mid-tier 50k～500k": "opth4jKpEb",
        "Mid-tier 50k～500k": "opth4jKpEb",  # API中的格式
    }
    
    # Tag选项映射
    TAG_OPTIONS = {
        "fitness": "optrNLU91H",
        "model": "optBCrTW6s",
        "ACG": "opt156379164",
        "muscle man": "opt0xkxi60",
        "diet": "opt2jCSF0W",
        "fitness coach": "optQOFWjfH",
        "hot body": "optGgEFgG3",
        "muscle girl": "optuf7hTO2",
        "mom": "optSADQ0Qp",
    }
    
    @classmethod
    def map_record(cls, api_record: Dict) -> Dict:
        """将API记录映射为飞书记录格式"""
        feishu_record = {"fields": {}}
        
        for field_name, (api_field, field_type) in cls.FIELD_MAPPING.items():
            value = api_record.get(api_field)
            
            if value is None:
                continue
                
            try:
                if field_type == "text":
                    if api_field == "filter_names" and isinstance(value, list):
                        feishu_record["fields"][field_name] = ", ".join(value)
                    else:
                        feishu_record["fields"][field_name] = str(value) if value else ""
                        
                elif field_type == "url":
                    if value and isinstance(value, str):
                        feishu_record["fields"][field_name] = {"link": value}
                        
                elif field_type == "number":
                    if isinstance(value, (int, float)) and value is not None:
                        # 处理百分比字段
                        if api_field == "engagement_rate" and value is not None:
                            feishu_record["fields"][field_name] = float(value) / 100.0  # 转换为小数
                        else:
                            feishu_record["fields"][field_name] = float(value)
                        
                elif field_type == "select":
                    if api_field == "source" and value:
                        # 对于单选字段，暂时直接使用值（可能需要后续映射选项ID）
                        feishu_record["fields"][field_name] = value
                    elif api_field == "level" and value:
                        feishu_record["fields"][field_name] = value
                            
                elif field_type == "multiselect":
                    if api_field == "tag_names" and isinstance(value, list):
                        # 对于多选字段，暂时直接使用值列表
                        feishu_record["fields"][field_name] = value
                            
            except Exception as e:
                logger.warning(f"映射字段 {api_field} 失败: {str(e)}")
                continue
        
        return feishu_record

class DataSynchronizer:
    """数据同步器"""
    
    def __init__(self, config: SyncConfig):
        self.config = config
        self.fetcher = DataFetcher(config)
        self.feishu_api = FeishuAPI(config)
        self.mapper = DataMapper()
        
    def sync_data(self, project_code: str = None, max_records: int = None, start_offset: int = None, start_page: int = None) -> Dict:
        """执行数据同步
        
        Args:
            project_code: 项目代码
            max_records: 最大同步记录数
            start_offset: 起始偏移量（从第几条记录开始，优先级高于start_page）
            start_page: 起始页数（从第几页开始，页数从1开始）
        """
        project_code = project_code or self.config.default_project_code
        max_records = min(max_records or self.config.max_records_per_table, self.config.max_records_per_table)
        
        # 确定起始偏移量
        if start_offset is not None:
            actual_start_offset = max(0, start_offset)
        elif start_page is not None:
            actual_start_offset = max(0, (start_page - 1) * self.config.db_batch_size)
        else:
            actual_start_offset = self.config.start_offset
        
        logger.info(f"开始同步数据 - 项目: {project_code}, 最大记录数: {max_records}, 起始偏移量: {actual_start_offset}")
        
        stats = {
            "total_fetched": 0,
            "total_synced": 0,
            "total_failed": 0,
            "batches_processed": 0,
            "start_time": time.time(),
            "start_offset": actual_start_offset
        }
        
        try:
            # 首次获取数据以确定总数
            first_batch = self.fetcher.fetch_data_batch(0, 1, project_code)
            total_available = first_batch.get("total", 0)
            
            if total_available == 0:
                logger.warning("没有可同步的数据")
                return stats
            
            # 检查起始偏移量是否超出范围
            if actual_start_offset >= total_available:
                logger.warning(f"起始偏移量 {actual_start_offset} 超出可用数据范围 {total_available}")
                return stats
                
            # 计算实际需要同步的数据量
            remaining_records = total_available - actual_start_offset
            total_to_sync = min(remaining_records, max_records)
            
            logger.info(f"可用数据: {total_available}, 起始偏移量: {actual_start_offset}, 剩余数据: {remaining_records}, 计划同步: {total_to_sync}")
            
            # 分批处理
            skip = actual_start_offset
            end_offset = actual_start_offset + total_to_sync
            
            while skip < end_offset:
                batch_size = min(self.config.db_batch_size, end_offset - skip)
                
                try:
                    # 获取数据批次
                    batch_data = self.fetcher.fetch_data_batch(skip, batch_size, project_code)
                    api_records = batch_data.get("items", [])
                    
                    if not api_records:
                        logger.warning(f"批次 {skip}-{skip + batch_size} 没有数据")
                        break
                    
                    # 转换数据格式
                    feishu_records = []
                    for record in api_records:
                        try:
                            mapped_record = self.mapper.map_record(record)
                            if mapped_record.get("fields"):
                                feishu_records.append(mapped_record)
                        except Exception as e:
                            logger.error(f"映射记录失败: {str(e)}")
                            stats["total_failed"] += 1
                    
                    stats["total_fetched"] += len(api_records)
                    
                    # 批量写入飞书
                    if feishu_records:
                        self._sync_records_to_feishu(feishu_records, stats)
                    
                    stats["batches_processed"] += 1
                    skip += batch_size
                    
                    # 进度报告
                    processed_records = skip - actual_start_offset
                    progress = min(100, (processed_records / total_to_sync) * 100)
                    current_page = (skip // self.config.db_batch_size) + 1
                    logger.info(f"同步进度: {progress:.1f}% ({processed_records}/{total_to_sync}) - 当前位置: 第{skip}条记录, 第{current_page}页")
                    
                except Exception as e:
                    logger.error(f"处理批次 {skip}-{skip + batch_size} 失败: {str(e)}")
                    stats["total_failed"] += batch_size
                    skip += batch_size
                    continue
            
            # 同步完成统计
            stats["end_time"] = time.time()
            stats["duration"] = stats["end_time"] - stats["start_time"]
            
            logger.info(f"同步完成 - 获取: {stats['total_fetched']}, 成功: {stats['total_synced']}, 失败: {stats['total_failed']}, 耗时: {stats['duration']:.2f}秒")
            
            return stats
            
        except Exception as e:
            logger.error(f"同步过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def _sync_records_to_feishu(self, records: List[Dict], stats: Dict):
        """将记录批量同步到飞书"""
        # 按飞书批次大小分组
        for i in range(0, len(records), self.config.feishu_batch_size):
            batch = records[i:i + self.config.feishu_batch_size]
            
            try:
                self.feishu_api.batch_create_records(batch)
                stats["total_synced"] += len(batch)
                
            except Exception as e:
                logger.error(f"飞书批量写入失败: {str(e)}")
                stats["total_failed"] += len(batch)
                
                # 尝试单条写入
                for record in batch:
                    try:
                        self.feishu_api.batch_create_records([record])
                        stats["total_synced"] += 1
                        stats["total_failed"] -= 1
                    except Exception as single_e:
                        logger.error(f"单条记录写入失败: {str(single_e)}")

def load_config() -> SyncConfig:
    """加载配置"""
    return SyncConfig(
        feishu_app_id=os.getenv("FEISHU_APP_ID"),
        feishu_app_secret=os.getenv("FEISHU_APP_SECRET"),
        feishu_app_token=os.getenv("FEISHU_APP_TOKEN"),
        feishu_table_id="tblJWKfG3OxvtHIt", # tblrj8d0FVxp5D2s tblMGQm7hGhvON2u  tblJWKfG3OxvtHIt tbl2QCxvwiKlksiN
        db_api_url=os.getenv("DB_API_BASE_URL"),
        db_api_timeout=int(os.getenv("DB_API_TIMEOUT", 30)),
        db_batch_size=int(os.getenv("DB_BATCH_SIZE", 100)),
        feishu_batch_size=int(os.getenv("FEISHU_BATCH_SIZE", 500)),
        max_records_per_table=20000,
        feishu_rate_limit=float(os.getenv("FEISHU_RATE_LIMIT", 0.1)),
        db_rate_limit=float(os.getenv("DB_RATE_LIMIT", 0.05)),
        max_retries=int(os.getenv("MAX_RETRIES", 3)),
        retry_delay=float(os.getenv("RETRY_DELAY", 1)),
        max_workers=int(os.getenv("MAX_WORKERS", 3)),
        default_project_code=os.getenv("DEFAULT_PROJECT_CODE", "NOTM105"),
        start_offset=int(os.getenv("START_OFFSET", 0)),
        start_page=int(os.getenv("START_PAGE", 1))
    )

def main():
    """主函数"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='数据同步脚本 - 从API获取数据并同步到飞书表格')
    parser.add_argument('--start-offset', type=int, help='起始偏移量（从第几条记录开始，例如：20000）')
    parser.add_argument('--start-page', type=int, help='起始页数（从第几页开始，页数从1开始）')
    parser.add_argument('--max-records', type=int, help='最大同步记录数')
    parser.add_argument('--project-code', type=str, help='项目代码')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config = load_config()
        
        # 验证必要配置
        required_configs = [
            config.feishu_app_id, config.feishu_app_secret,
            config.feishu_app_token, config.feishu_table_id,
            config.db_api_url
        ]
        
        if not all(required_configs):
            logger.error("缺少必要的配置项，请检查.env文件")
            return 1
        
        # 创建同步器并开始同步
        synchronizer = DataSynchronizer(config)
        
        # 使用命令行参数或配置文件中的默认值
        stats = synchronizer.sync_data(
            project_code=args.project_code,
            max_records=args.max_records,
            start_offset=args.start_offset,
            start_page=args.start_page
        )
        
        # 输出最终统计
        print(f"\n同步统计:")
        print(f"- 起始偏移量: {stats.get('start_offset', 0)}")
        print(f"- 获取记录数: {stats['total_fetched']}")
        print(f"- 成功同步数: {stats['total_synced']}")
        print(f"- 失败记录数: {stats['total_failed']}")
        print(f"- 处理批次数: {stats['batches_processed']}")
        print(f"- 总耗时: {stats.get('duration', 0):.2f}秒")
        
        # 输出下次同步建议
        if stats['total_synced'] > 0:
            next_offset = stats.get('start_offset', 0) + stats['total_synced']
            next_page = (next_offset // config.db_batch_size) + 1
            print(f"\n下次同步建议:")
            print(f"- 使用偏移量: --start-offset {next_offset}")
            print(f"- 或使用页数: --start-page {next_page}")
        
        return 0 if stats['total_failed'] == 0 else 1
        
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())