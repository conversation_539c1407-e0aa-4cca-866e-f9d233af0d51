#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书数据更新脚本 - 更新Send Date和Send Status字段
从飞书获取email不为空的记录，通过KOL ID查询Send Data API，并批量更新飞书数据
"""

import os
import sys
import json
import time
import logging
import requests
import datetime
import urllib.parse
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from dotenv import load_dotenv
import traceback
import argparse

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_send_data.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class UpdateConfig:
    """更新配置类"""
    # 飞书配置
    feishu_app_id: str
    feishu_app_secret: str
    feishu_app_token: str
    feishu_table_id: str
    
    # Send Data API配置
    send_data_api_url: str
    send_data_api_timeout: int
    
    # 分页配置
    feishu_page_size: int
    
    # 速率限制
    feishu_rate_limit: float
    send_data_rate_limit: float
    
    # 重试配置
    max_retries: int
    retry_delay: float

class FeishuUpdateAPI:
    """飞书更新API客户端"""
    
    def __init__(self, config: UpdateConfig):
        self.config = config
        self.access_token: Optional[str] = None
        self.token_expires_at: float = 0
        self.session = requests.Session()
        
    def get_access_token(self) -> str:
        """获取或刷新访问令牌"""
        current_time = time.time()
        
        if self.access_token and current_time < self.token_expires_at:
            return self.access_token
            
        url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        data = {
            "app_id": self.config.feishu_app_id,
            "app_secret": self.config.feishu_app_secret
        }
        
        response = self.session.post(url, json=data)
        response.raise_for_status()
        
        result = response.json()
        if result.get("code") != 0:
            raise Exception(f"获取访问令牌失败: {result.get('msg')}")
            
        self.access_token = result["tenant_access_token"]
        self.token_expires_at = current_time + result["expire"] - 300  # 提前5分钟刷新
        
        logger.info("成功获取飞书访问令牌")
        return self.access_token
    
    def search_records_with_email(self, page_token: str = None) -> Dict:
        """搜索email不为空的记录"""
        access_token = self.get_access_token()
        
        # 构建URL
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.config.feishu_app_token}/tables/{self.config.feishu_table_id}/records/search"
        params = {
            "page_size": self.config.feishu_page_size
        }
        if page_token:
            params["page_token"] = page_token
        
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        
        # 构建查询条件
        data = {
            "automatic_fields": False,
            "filter": {
                "conditions": [
                    {
                        "field_name": "Email",
                        "operator": "isNotEmpty",
                        "value": []
                    }
                ],
                "conjunction": "and"
            }
        }
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(url, headers=headers, json=data, params=params, timeout=30)
                response.raise_for_status()
                
                result = response.json()
                if result.get("code") != 0:
                    if result.get("code") == 99991663:  # token过期
                        self.access_token = None
                        access_token = self.get_access_token()
                        headers["Authorization"] = f"Bearer {access_token}"
                        continue
                    raise Exception(f"搜索记录失败: {result.get('msg')}")
                
                return result
                
            except Exception as e:
                logger.warning(f"搜索记录失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))
        
        # 速率限制
        time.sleep(self.config.feishu_rate_limit)
    
    def batch_update_records(self, records: List[Dict]) -> Dict:
        """批量更新记录"""
        if not records:
            return {"code": 0, "data": {"records": []}}

        access_token = self.get_access_token()
        url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{self.config.feishu_app_token}/tables/{self.config.feishu_table_id}/records/batch_update"

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        data = {"records": records}

        for attempt in range(self.config.max_retries):
            try:
                response = self.session.post(url, headers=headers, json=data, timeout=30)
                response.raise_for_status()

                result = response.json()
                if result.get("code") != 0:
                    if result.get("code") == 99991663:  # token过期
                        self.access_token = None
                        access_token = self.get_access_token()
                        headers["Authorization"] = f"Bearer {access_token}"
                        continue
                    raise Exception(f"批量更新记录失败: {result.get('msg')}")

                logger.info(f"✅ 成功更新 {len(records)} 条记录")
                return result

            except Exception as e:
                logger.warning(f"批量更新记录失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))

        # 速率限制
        time.sleep(self.config.feishu_rate_limit)

class SendDataAPI:
    """Send Data API客户端"""
    
    def __init__(self, config: UpdateConfig):
        self.config = config
        self.session = requests.Session()
    
    def get_send_data(self, kol_id: str) -> Optional[Dict]:
        """获取KOL的发送数据"""
        # 清理KOL ID中的特殊字符和emoji
        cleaned_kol_id = self._clean_kol_id(kol_id)
        if not cleaned_kol_id:
            logger.warning(f"KOL ID清理后为空: {kol_id}")
            return None

        # 添加TK_前缀
        api_kol_id = f"TK_{cleaned_kol_id}"

        # URL编码处理
        encoded_kol_id = urllib.parse.quote(api_kol_id, safe='')
        url = f"{self.config.send_data_api_url}/{encoded_kol_id}"
        params = {
            "page": 1,
            "size": 10
        }
        
        headers = {
            "accept": "application/json"
        }
        
        for attempt in range(self.config.max_retries):
            try:
                response = self.session.get(
                    url, 
                    headers=headers, 
                    params=params, 
                    timeout=self.config.send_data_api_timeout
                )
                response.raise_for_status()
                
                result = response.json()
                items = result.get("items", [])
                
                if not items:
                    logger.debug(f"KOL {kol_id} 没有发送数据")
                    return None
                
                # 按created_at排序，取最新的记录
                latest_item = max(items, key=lambda x: x.get("created_at", ""))
                
                logger.debug(f"KOL {kol_id} 获取到发送数据: {latest_item.get('send_status')}, {latest_item.get('send_date')}")
                return latest_item
                
            except Exception as e:
                logger.warning(f"获取发送数据失败 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                if attempt == self.config.max_retries - 1:
                    logger.error(f"KOL {kol_id} 获取发送数据最终失败")
                    return None
                time.sleep(self.config.retry_delay * (2 ** attempt))
        
        # 速率限制
        time.sleep(self.config.send_data_rate_limit)
        return None

    def _clean_kol_id(self, kol_id: str) -> str:
        """清理KOL ID，移除特殊字符和emoji"""
        import re

        # 移除emoji和特殊Unicode字符，只保留ASCII字符、数字、下划线、连字符和点
        cleaned = re.sub(r'[^\w\-\.]', '', kol_id)

        # 移除多余的空格和特殊字符
        cleaned = cleaned.strip()

        return cleaned

class DataUpdater:
    """数据更新器"""
    
    def __init__(self, config: UpdateConfig):
        self.config = config
        self.feishu_api = FeishuUpdateAPI(config)
        self.send_api = SendDataAPI(config)
    
    def extract_kol_id(self, record: Dict) -> Optional[str]:
        """从记录中提取KOL ID"""
        fields = record.get("fields", {})
        kol_id_field = fields.get("KOL ID")
        
        if not kol_id_field:
            return None
        
        # 处理不同的字段格式
        if isinstance(kol_id_field, list) and len(kol_id_field) > 0:
            if isinstance(kol_id_field[0], dict):
                return kol_id_field[0].get("text", "").strip()
            else:
                return str(kol_id_field[0]).strip()
        elif isinstance(kol_id_field, str):
            return kol_id_field.strip()
        
        return None
    
    def convert_iso_to_timestamp_ms(self, iso_time: str) -> int:
        """将ISO时间格式转换为毫秒级时间戳"""
        try:
            # 解析ISO时间
            dt = datetime.datetime.fromisoformat(iso_time.replace('Z', '+00:00'))
            # 转换为毫秒级时间戳
            timestamp_ms = int(dt.timestamp() * 1000)
            return timestamp_ms
        except Exception as e:
            logger.error(f"时间转换失败: {iso_time} -> {str(e)}")
            return 0

    def update_send_data(self, dry_run: bool = False) -> Dict:
        """执行数据更新

        Args:
            dry_run: 是否为试运行模式（不实际更新数据）
        """
        logger.info(f"开始更新Send Date和Send Status数据 - 模式: {'试运行' if dry_run else '正式运行'}")

        stats = {
            "total_processed": 0,
            "total_updated": 0,
            "total_failed": 0,
            "total_no_data": 0,
            "start_time": time.time()
        }

        try:
            page_token = None

            while True:
                # 获取一页数据
                result = self.feishu_api.search_records_with_email(page_token)
                data = result.get("data", {})
                items = data.get("items", [])

                if not items:
                    logger.info("没有更多记录需要处理")
                    break

                logger.info(f"📄 处理当前页 {len(items)} 条记录")

                # 收集当前页需要更新的记录
                page_update_records = []

                # 处理当前页的记录
                for record in items:
                    stats["total_processed"] += 1
                    record_id = record.get("record_id")

                    try:
                        # 提取KOL ID
                        kol_id = self.extract_kol_id(record)
                        if not kol_id:
                            logger.warning(f"记录 {record_id} 没有有效的KOL ID")
                            stats["total_failed"] += 1
                            continue

                        # 获取发送数据
                        send_data = self.send_api.get_send_data(kol_id)
                        if not send_data:
                            logger.debug(f"KOL {kol_id} 没有发送数据")
                            stats["total_no_data"] += 1
                            continue

                        # 准备更新字段
                        update_fields = {}

                        # 处理Send Date
                        send_date = send_data.get("send_date")
                        if send_date:
                            timestamp_ms = self.convert_iso_to_timestamp_ms(send_date)
                            if timestamp_ms > 0:
                                update_fields["Send Date"] = timestamp_ms

                        # 处理Send Status
                        send_status = send_data.get("send_status")
                        if send_status:
                            update_fields["Send Status"] = send_status

                        # 如果有需要更新的字段
                        if update_fields:
                            update_record = {
                                "record_id": record_id,
                                "fields": update_fields
                            }
                            page_update_records.append(update_record)

                            logger.debug(f"准备更新记录 {record_id}: {json.dumps(update_fields, ensure_ascii=False)}")

                    except Exception as e:
                        logger.error(f"处理记录 {record_id} 失败: {str(e)}")
                        stats["total_failed"] += 1
                        continue

                # 更新当前页的记录
                if page_update_records:
                    if not dry_run:
                        logger.info(f"🔄 开始更新当前页 {len(page_update_records)} 条记录")
                        self._execute_page_update(page_update_records, stats)
                    else:
                        logger.info(f"🧪 试运行模式：跳过更新当前页 {len(page_update_records)} 条记录")
                        stats["total_updated"] += len(page_update_records)
                else:
                    logger.info(f"📝 当前页没有需要更新的记录")

                # 检查是否有下一页
                has_more = data.get("has_more", False)
                if not has_more:
                    break

                page_token = data.get("page_token")
                if not page_token:
                    break

                # 进度报告
                logger.info(f"📊 已处理 {stats['total_processed']} 条记录，已更新 {stats['total_updated']} 条记录")

            # 完成统计
            stats["end_time"] = time.time()
            stats["duration"] = stats["end_time"] - stats["start_time"]

            logger.info(f"更新完成 - 处理: {stats['total_processed']}, 更新: {stats['total_updated']}, 失败: {stats['total_failed']}, 无数据: {stats['total_no_data']}, 耗时: {stats['duration']:.2f}秒")

            return stats

        except Exception as e:
            logger.error(f"更新过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def _execute_page_update(self, records: List[Dict], stats: Dict):
        """执行当前页的记录更新"""
        try:
            logger.info(f"🚀 正在更新 {len(records)} 条记录...")
            self.feishu_api.batch_update_records(records)
            stats["total_updated"] += len(records)
            logger.info(f"✅ 当前页更新成功: {len(records)} 条记录")

        except Exception as e:
            logger.error(f"❌ 当前页批量更新失败: {str(e)}")
            logger.info(f"🔄 尝试单条更新...")

            # 尝试单条更新
            success_count = 0
            for record in records:
                try:
                    self.feishu_api.batch_update_records([record])
                    success_count += 1
                    logger.debug(f"✅ 单条更新成功: {record.get('record_id')}")
                except Exception as single_e:
                    logger.error(f"❌ 单条记录更新失败: {record.get('record_id')} - {str(single_e)}")

            stats["total_updated"] += success_count
            stats["total_failed"] += len(records) - success_count
            logger.info(f"📊 单条更新结果: 成功 {success_count}/{len(records)} 条")

def load_config() -> UpdateConfig:
    """加载配置"""
    return UpdateConfig(
        feishu_app_id=os.getenv("FEISHU_APP_ID"),
        feishu_app_secret=os.getenv("FEISHU_APP_SECRET"),
        feishu_app_token=os.getenv("FEISHU_APP_TOKEN"),
        feishu_table_id=os.getenv("FEISHU_TABLE_ID"),
        send_data_api_url=os.getenv("SEND_DATA_API_URL", "http://54.84.111.234:8000/api/v1/send-data/kol"),
        send_data_api_timeout=int(os.getenv("SEND_DATA_API_TIMEOUT", 30)),
        feishu_batch_size=int(os.getenv("FEISHU_BATCH_SIZE", 500)),
        feishu_page_size=int(os.getenv("FEISHU_PAGE_SIZE", 100)),
        feishu_rate_limit=float(os.getenv("FEISHU_RATE_LIMIT", 0.1)),
        send_data_rate_limit=float(os.getenv("SEND_DATA_RATE_LIMIT", 0.05)),
        max_retries=int(os.getenv("MAX_RETRIES", 3)),
        retry_delay=float(os.getenv("RETRY_DELAY", 1))
    )

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='飞书数据更新脚本 - 更新Send Date和Send Status字段')
    parser.add_argument('--dry-run', action='store_true', help='试运行模式（不实际更新数据）')

    args = parser.parse_args()

    try:
        # 加载配置
        config = load_config()

        # 验证必要配置
        required_configs = [
            config.feishu_app_id, config.feishu_app_secret,
            config.feishu_app_token, config.feishu_table_id,
            config.send_data_api_url
        ]

        if not all(required_configs):
            logger.error("缺少必要的配置项，请检查.env文件")
            return 1

        # 创建更新器并开始更新
        updater = DataUpdater(config)
        stats = updater.update_send_data(dry_run=args.dry_run)

        # 输出最终统计
        print(f"\n更新统计:")
        print(f"- 处理记录数: {stats['total_processed']}")
        print(f"- 成功更新数: {stats['total_updated']}")
        print(f"- 失败记录数: {stats['total_failed']}")
        print(f"- 无数据记录数: {stats['total_no_data']}")
        print(f"- 总耗时: {stats.get('duration', 0):.2f}秒")

        return 0 if stats['total_failed'] == 0 else 1

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
