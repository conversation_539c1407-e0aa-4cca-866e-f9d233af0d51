#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 测试非主键字段
"""

import os
import json
import requests
from dotenv import load_dotenv
import uuid

load_dotenv()

def get_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    data = {
        "app_id": os.getenv("FEISHU_APP_ID"),
        "app_secret": os.getenv("FEISHU_APP_SECRET")
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result.get("code") != 0:
        raise Exception(f"获取访问令牌失败: {result.get('msg')}")
    
    return result["tenant_access_token"]

def test_create_record():
    """测试创建记录"""
    access_token = get_access_token()
    
    # 创建唯一的KOL ID
    unique_id = f"TEST_{uuid.uuid4().hex[:8].upper()}"
    
    # 测试记录
    test_record = {
        "fields": {
            "fldfX7JJlA": unique_id,  # KOL ID (主键) - 使用唯一值
            "fldtPzB7PT": "Test KOL Name",  # KOL Name
            "fldgCMdgRT": "Test Bio Content",  # Bio
        }
    }
    
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{os.getenv('FEISHU_APP_TOKEN')}/tables/{os.getenv('FEISHU_TABLE_ID')}/records/batch_create"
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {"records": [test_record]}
    
    print("发送数据:")
    print(json.dumps(data, indent=2, ensure_ascii=False))
    
    response = requests.post(url, headers=headers, json=data)
    result = response.json()
    
    print(f"\n响应状态码: {response.status_code}")
    print("响应内容:")
    print(json.dumps(result, indent=2, ensure_ascii=False))
    
    return result.get("code") == 0

def main():
    try:
        success = test_create_record()
        if success:
            print("\n✅ 测试成功! 字段映射正确")
        else:
            print("\n❌ 测试失败")
            
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()