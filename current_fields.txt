飞书表格字段信息:
================================================================================
字段ID: fldfX7JJlA
字段名: KOL ID
类型: 1 (Text)
主键: True
----------------------------------------
字段ID: fld474L7vA
字段名: Manual Status
类型: 3 (SingleSelect)
主键: False
属性: {
  "options": [
    {
      "color": 0,
      "id": "opt8GIBkez",
      "name": "OOG120-Mismatch"
    },
    {
      "color": 1,
      "id": "optT1ecqza",
      "name": "OOG120-Matched"
    }
  ]
}
----------------------------------------
字段ID: fld9z4F71u
字段名: Email
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fld0M0k2s5
字段名: Account link
类型: 15 (Url)
主键: False
----------------------------------------
字段ID: fldAZhKkcc
字段名: Send Status
类型: 3 (SingleSelect)
主键: False
属性: {
  "options": [
    {
      "color": 0,
      "id": "optyMoOWJ0",
      "name": "第四轮"
    },
    {
      "color": 1,
      "id": "optwSVdgWs",
      "name": "第五轮"
    },
    {
      "color": 2,
      "id": "optaItXBwb",
      "name": "第七轮"
    },
    {
      "color": 3,
      "id": "opt5nGVx7X",
      "name": "第六轮"
    },
    {
      "color": 4,
      "id": "opt2Shwl3t",
      "name": "第九轮"
    },
    {
      "color": 5,
      "id": "optLlgCuwb",
      "name": "第十轮"
    },
    {
      "color": 6,
      "id": "optoEczhQ3",
      "name": "第十一轮"
    },
    {
      "color": 7,
      "id": "optAwgP9zf",
      "name": "第十二轮"
    },
    {
      "color": 8,
      "id": "opt1hLPS3v",
      "name": "第十三轮"
    },
    {
      "color": 9,
      "id": "opt1r4ENWJ",
      "name": "第十四轮"
    },
    {
      "color": 10,
      "id": "optYev5sZY",
      "name": "第十五轮"
    },
    {
      "color": 0,
      "id": "opttEy1vIJ",
      "name": "第十六轮"
    },
    {
      "color": 1,
      "id": "opt1945411368",
      "name": "Round No.17"
    },
    {
      "color": 2,
      "id": "opt1945411367",
      "name": "Round No.18"
    },
    {
      "color": 3,
      "id": "optZvvD0Zk",
      "name": "Round No.19"
    },
    {
      "color": 4,
      "id": "optCisfV26",
      "name": "Round No.20"
    },
    {
      "color": 5,
      "id": "opt1945411343",
      "name": "Round No.21"
    },
    {
      "color": 6,
      "id": "opt1175870138",
      "name": "Failed"
    },
    {
      "color": 7,
      "id": "opt1945411340",
      "name": "Round No.24"
    },
    {
      "color": 8,
      "id": "opt1945411339",
      "name": "Round No.25"
    },
    {
      "color": 9,
      "id": "opt1945411338",
      "name": "Round No.26"
    },
    {
      "color": 10,
      "id": "opt1945411337",
      "name": "Round No.27"
    },
    {
      "color": 0,
      "id": "opt1945411336",
      "name": "Round No.28"
    },
    {
      "color": 1,
      "id": "opt1945411335",
      "name": "Round No.29"
    },
    {
      "color": 2,
      "id": "opt1945411310",
      "name": "Round No.33"
    },
    {
      "color": 3,
      "id": "opt1945411309",
      "name": "Round No.34"
    },
    {
      "color": 4,
      "id": "opt1945411308",
      "name": "Round No.35"
    },
    {
      "color": 5,
      "id": "opt1945411307",
      "name": "Round No.36"
    },
    {
      "color": 6,
      "id": "optuFvcuI8",
      "name": "Round No.30"
    },
    {
      "color": 7,
      "id": "optUiufOxA",
      "name": "Round No.31"
    },
    {
      "color": 8,
      "id": "optM5KeDS9",
      "name": "Round No.32"
    }
  ]
}
----------------------------------------
字段ID: fld3o9DqAx
字段名: Send Date
类型: 5 (DateTime)
主键: False
属性: {
  "auto_fill": false,
  "date_formatter": "yyyy/MM/dd"
}
----------------------------------------
字段ID: fldtPzB7PT
字段名: KOL Name
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fldgCMdgRT
字段名: Bio
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fld6E3EtrK
字段名: Export Date
类型: 5 (DateTime)
主键: False
属性: {
  "auto_fill": true,
  "date_formatter": "yyyy/MM/dd"
}
----------------------------------------
字段ID: fld0WDvqqK
字段名: Source
类型: 3 (SingleSelect)
主键: False
属性: {
  "options": [
    {
      "color": 0,
      "id": "opt45iCdi3",
      "name": "Collabstr"
    },
    {
      "color": 1,
      "id": "optWy0UYXu",
      "name": "Manual"
    },
    {
      "color": 2,
      "id": "opt9KMQetm",
      "name": "Creable"
    },
    {
      "color": 3,
      "id": "opt1115032617",
      "name": "Heepsy"
    },
    {
      "color": 4,
      "id": "opt962695875",
      "name": "Modash"
    }
  ]
}
----------------------------------------
字段ID: fldH8kFQIv
字段名: Filter
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fld4SFEiCA
字段名: Gender
类型: 3 (SingleSelect)
主键: False
属性: {
  "options": [
    {
      "color": 0,
      "id": "optFTjgax9",
      "name": "Male"
    },
    {
      "color": 1,
      "id": "opt0ggyjVn",
      "name": "Female"
    },
    {
      "color": 2,
      "id": "opt553142582",
      "name": "MALE"
    },
    {
      "color": 6,
      "id": "opt199jkDe",
      "name": "LGBT"
    },
    {
      "color": 3,
      "id": "opt1202605003",
      "name": "FEMALE"
    }
  ]
}
----------------------------------------
字段ID: fldz8OOg2o
字段名: Tag
类型: 4 (MultiSelect)
主键: False
属性: {
  "options": [
    {
      "color": 7,
      "id": "optrNLU91H",
      "name": "fitness"
    },
    {
      "color": 0,
      "id": "opt0xkxi60",
      "name": "muscle man"
    },
    {
      "color": 15,
      "id": "opt2jCSF0W",
      "name": "diet"
    },
    {
      "color": 2,
      "id": "optQOFWjfH",
      "name": "fitness coach"
    },
    {
      "color": 8,
      "id": "optGgEFgG3",
      "name": "hot body"
    },
    {
      "color": 1,
      "id": "optuf7hTO2",
      "name": "muscle girl"
    },
    {
      "color": 19,
      "id": "optSADQ0Qp",
      "name": "mom"
    },
    {
      "color": 6,
      "id": "optBCrTW6s",
      "name": "model"
    },
    {
      "color": 3,
      "id": "opt156379164",
      "name": "ACG"
    }
  ]
}
----------------------------------------
字段ID: fld0FhflKc
字段名: Language
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fld46fWZdV
字段名: Location
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fldQ5A1fxI
字段名: Followers(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldyy9fTfl
字段名: Likes(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldtXe7t67
字段名: Mean Views(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldvOXBJ4Y
字段名: Median Views(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldisfzdXV
字段名: Keywords-AI
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fldcTuUNb8
字段名: Level
类型: 3 (SingleSelect)
主键: False
属性: {
  "options": [
    {
      "color": 0,
      "id": "optgzYkasF",
      "name": "Nano 1~10k"
    },
    {
      "color": 1,
      "id": "optdUw4abJ",
      "name": "Micro 10~50k"
    },
    {
      "color": 2,
      "id": "opth4jKpEb",
      "name": "Mid-tier 50~500k"
    }
  ]
}
----------------------------------------
字段ID: fld7DZtWV0
字段名: Engagement Rate(%)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.00%"
}
----------------------------------------
字段ID: fldwhSDlK1
字段名: Average Views(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldWdlwluT
字段名: Average Likes(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fld41K0Izr
字段名: Average Comments(K)
类型: 2 (Number)
主键: False
属性: {
  "formatter": "0.0"
}
----------------------------------------
字段ID: fldLaL24dY
字段名: Most used hashtags
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fld9z6se2p
字段名: Slug
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fldnKqNue7
字段名: Creator ID
类型: 1 (Text)
主键: False
----------------------------------------
字段ID: fldXCTzxHI
字段名: Label
类型: 19 (Lookup)
主键: False
属性: {
  "filter_info": {
    "conditions": {
      "children": [
        {
          "field_id": "fldxYc44EL",
          "field_type": 1,
          "operator": "is",
          "value": null
        }
      ],
      "conjunction": "and"
    },
    "target_table": "tblLD6m8ls5X5vVZ"
  },
  "formatter": "",
  "formula": "bitable::$table[tblLD6m8ls5X5vVZ].FILTER(CurrentValue.$column[fldxYc44EL]=bitable::$table[tbljBVnXlOLRx5dV].$field[fldfX7JJlA]).$column[fldCWSuOPT].LISTCOMBINE()",
  "roll_up": 0,
  "target_field": "fldCWSuOPT"
}
----------------------------------------

建议的字段映射:
FIELD_MAPPING = {
    "fldfX7JJlA": ("kol_id", "text"),  # KOL ID
    # "fld474L7vA": ("", "select"),  # Manual Status
    "fld9z4F71u": ("email", "text"),  # Email
    "fld0M0k2s5": ("account_link", "url"),  # Account link
    # "fldAZhKkcc": ("", "select"),  # Send Status
    # "fld3o9DqAx": ("", "datetime"),  # Send Date
    "fldtPzB7PT": ("kol_name", "text"),  # KOL Name
    "fldgCMdgRT": ("bio", "text"),  # Bio
    # "fld6E3EtrK": ("", "datetime"),  # Export Date
    "fld0WDvqqK": ("source", "select"),  # Source
    "fldH8kFQIv": ("filter_names", "text"),  # Filter
    # "fld4SFEiCA": ("", "select"),  # Gender
    # "fldz8OOg2o": ("", "select"),  # Tag
    # "fld0FhflKc": ("", "text"),  # Language
    # "fld46fWZdV": ("", "text"),  # Location
    "fldQ5A1fxI": ("followers_k", "number"),  # Followers(K)
    "fldyy9fTfl": ("likes_k", "number"),  # Likes(K)
    "fldtXe7t67": ("mean_views_k", "number"),  # Mean Views(K)
    "fldvOXBJ4Y": ("median_views_k", "number"),  # Median Views(K)
    "fldisfzdXV": ("keywords_ai", "text"),  # Keywords-AI
    # "fldcTuUNb8": ("", "select"),  # Level
    # "fld7DZtWV0": ("", "number"),  # Engagement Rate(%)
    # "fldwhSDlK1": ("", "number"),  # Average Views(K)
    "fldWdlwluT": ("likes_k", "number"),  # Average Likes(K)
    # "fld41K0Izr": ("", "number"),  # Average Comments(K)
    # "fldLaL24dY": ("", "text"),  # Most used hashtags
    # "fld9z6se2p": ("", "text"),  # Slug
    # "fldnKqNue7": ("", "text"),  # Creator ID
    # "fldXCTzxHI": ("", "text"),  # Label
}
